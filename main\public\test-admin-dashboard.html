<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background-color: #1e40af;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .info {
            background-color: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        .link {
            color: #1e40af;
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Admin Dashboard Test</h1>
        <p>Test admin login and verify all sidebar sections are working.</p>
    </div>

    <div class="container">
        <h2>🔍 Step 1: Test Admin Login</h2>
        <p><strong>Credentials:</strong> <EMAIL> / SuperAdmin@123</p>
        
        <button onclick="testAdminLogin()">🧪 Test Admin Login</button>
        <div id="loginResult"></div>
    </div>

    <div class="container">
        <h2>🔍 Step 2: Test Admin Dashboard Access</h2>
        <p>After successful login, test if dashboard loads without permission errors.</p>
        
        <button onclick="testDashboardAccess()">🧪 Test Dashboard Access</button>
        <div id="dashboardResult"></div>
    </div>

    <div class="container">
        <h2>🔍 Step 3: Manual Testing Links</h2>
        <p>After successful login, manually test these admin sections:</p>
        
        <div style="margin: 10px 0;">
            <span class="link" onclick="openAdminPage('/dashboard')">📊 Dashboard</span> |
            <span class="link" onclick="openAdminPage('/sales')">💰 Sales</span> |
            <span class="link" onclick="openAdminPage('/messages')">💬 Messages</span> |
            <span class="link" onclick="openAdminPage('/clients')">👥 Clients</span> |
            <span class="link" onclick="openAdminPage('/brokers')">🤝 Brokers</span> |
            <span class="link" onclick="openAdminPage('/admin-users')">👨‍💼 Admin Users</span> |
            <span class="link" onclick="openAdminPage('/settings')">⚙️ Settings</span>
        </div>
        
        <div id="manualTestResult"></div>
    </div>

    <div class="container">
        <h2>✅ Expected Results</h2>
        <ul>
            <li>✅ Admin login successful without 500 errors</li>
            <li>✅ Dashboard loads without "hasPermission is not a function" error</li>
            <li>✅ Sidebar shows: Sales, Messages, Authentication, Components, Settings sections</li>
            <li>✅ All navigation links work properly</li>
            <li>✅ Permission-based visibility working correctly</li>
        </ul>
    </div>

    <script>
        async function testAdminLogin() {
            showResult('loginResult', '🚀 Testing admin login...\n', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        email: '<EMAIL>',
                        password: 'SuperAdmin@123'
                    })
                });
                
                const data = await response.json();
                appendResult('loginResult', `📊 Login Response (${response.status}):\n${JSON.stringify(data, null, 2)}\n\n`);
                
                if (response.ok) {
                    appendResult('loginResult', '✅ ADMIN LOGIN SUCCESSFUL!\n', 'success');
                    appendResult('loginResult', '🔗 You can now test the dashboard manually.\n');
                } else {
                    appendResult('loginResult', '❌ ADMIN LOGIN FAILED!\n', 'error');
                }
                
            } catch (error) {
                appendResult('loginResult', `❌ Network Error: ${error.message}\n`, 'error');
            }
        }
        
        async function testDashboardAccess() {
            showResult('dashboardResult', '🚀 Testing dashboard access...\n', 'info');
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                appendResult('dashboardResult', `📊 Auth Check Response (${response.status}):\n${JSON.stringify(data, null, 2)}\n\n`);
                
                if (response.ok && data.authenticated) {
                    appendResult('dashboardResult', '✅ AUTHENTICATION VERIFIED!\n', 'success');
                    appendResult('dashboardResult', '🔗 Dashboard should load without permission errors.\n');
                } else {
                    appendResult('dashboardResult', '❌ NOT AUTHENTICATED!\n', 'error');
                    appendResult('dashboardResult', '🔗 Please login first using Step 1.\n');
                }
                
            } catch (error) {
                appendResult('dashboardResult', `❌ Network Error: ${error.message}\n`, 'error');
            }
        }
        
        function openAdminPage(path) {
            const url = `http://localhost:3001${path}`;
            window.open(url, '_blank');
            showResult('manualTestResult', `🔗 Opened: ${url}\n\nCheck if the page loads without errors and sidebar sections are visible.`, 'info');
        }
        
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function appendResult(elementId, message, type = '') {
            const element = document.getElementById(elementId);
            const currentDiv = element.querySelector('.result');
            if (currentDiv) {
                currentDiv.textContent += message;
                if (type) {
                    currentDiv.className = `result ${type}`;
                }
            } else {
                showResult(elementId, message, type || 'info');
            }
        }
    </script>
</body>
</html>
