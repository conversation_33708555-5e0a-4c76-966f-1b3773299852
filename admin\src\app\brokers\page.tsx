'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Sidebar from '@/components/dashboard/Sidebar';
import AdminTopNavbar from '@/components/AdminTopNavbar';
import {
  Users,
  Plus,
  Search,
  Filter,
  Star,
  Phone,
  Mail,
  MapPin,
  TrendingUp,
  Award,
  Clock,
  Building,
  DollarSign,
  Eye,
  Edit,
  MoreVertical
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Broker {
  id: string;
  full_name: string;
  email: string;
  contact_number: string;
  specialization?: string;
  experience_years?: number;
  office_address?: string;
  license_number?: string;
  status?: string;
  created_at?: string;
}

export default function BrokersPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [filterSpecialization, setFilterSpecialization] = useState<string>('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [brokers, setBrokers] = useState<Broker[]>([]);
  const [loading, setLoading] = useState(true);
  const [summaryData, setSummaryData] = useState([
    {
      title: 'Total Brokers',
      value: '0',
      icon: <Users size={20} className="text-blue-600" />,
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Active Brokers',
      value: '0',
      icon: <TrendingUp size={20} className="text-green-600" />,
      bgColor: 'bg-green-50'
    },
    {
      title: 'Total Sales',
      value: '₹0',
      icon: <DollarSign size={20} className="text-purple-600" />,
      bgColor: 'bg-purple-50'
    },
    {
      title: 'Properties Sold',
      value: '0',
      icon: <Building size={20} className="text-orange-600" />,
      bgColor: 'bg-orange-50'
    }
  ]);

  const brokersPerPage = 6;

  // Fetch real broker data from database
  useEffect(() => {
    const fetchBrokers = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/brokers');
        if (response.ok) {
          const data = await response.json();
          setBrokers(data.brokers || []);

          // Update summary data with real counts
          const totalBrokers = data.brokers?.length || 0;
          const activeBrokers = data.brokers?.filter((b: Broker) => b.status === 'active').length || 0;

          setSummaryData([
            {
              title: 'Total Brokers',
              value: totalBrokers.toString(),
              icon: <Users size={20} className="text-blue-600" />,
              bgColor: 'bg-blue-50'
            },
            {
              title: 'Active Brokers',
              value: activeBrokers.toString(),
              icon: <TrendingUp size={20} className="text-green-600" />,
              bgColor: 'bg-green-50'
            },
            {
              title: 'Total Sales',
              value: '₹0', // Will be calculated from real data
              icon: <DollarSign size={20} className="text-purple-600" />,
              bgColor: 'bg-purple-50'
            },
            {
              title: 'Properties Sold',
              value: '0', // Will be calculated from real data
              icon: <Building size={20} className="text-orange-600" />,
              bgColor: 'bg-orange-50'
            }
          ]);
        } else {
          toast.error('Failed to fetch brokers');
        }
      } catch (error) {
        console.error('Error fetching brokers:', error);
        toast.error('Error loading brokers');
      } finally {
        setLoading(false);
      }
    };

    fetchBrokers();
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Filter brokers based on search and filters
  const filteredBrokers = brokers.filter(broker => {
    const matchesSearch = broker.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         broker.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         broker.contact_number.includes(searchTerm);

    const matchesStatus = filterStatus === 'All' || broker.status === filterStatus.toLowerCase();

    const matchesSpecialization = filterSpecialization === 'All' ||
                                 (broker.specialization && broker.specialization.toLowerCase().includes(filterSpecialization.toLowerCase()));

    return matchesSearch && matchesStatus && matchesSpecialization;
  });

  // Pagination
  const totalPages = Math.ceil(filteredBrokers.length / brokersPerPage);
  const startIndex = (currentPage - 1) * brokersPerPage;
  const currentBrokers = filteredBrokers.slice(startIndex, startIndex + brokersPerPage);

  const handleViewBroker = (brokerId: string) => {
    router.push(`/brokers/${brokerId}`);
  };

  const handleEditBroker = (brokerId: string) => {
    router.push(`/brokers/${brokerId}/edit`);
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)} Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)} L`;
    } else {
      return `₹${amount.toLocaleString('en-IN')}`;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={16}
        className={index < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };



  return (
    <div className="flex min-h-screen bg-gray-50">
      <Sidebar isOpen={isSidebarOpen} closeSidebar={() => setIsSidebarOpen(false)} />

      <div className={`flex-1 transition-all duration-300 ${isSidebarOpen ? 'ml-[200px]' : 'ml-0'}`}>
        {/* Top Navbar */}
        <div className="sticky top-0 z-10">
          <AdminTopNavbar toggleSidebar={toggleSidebar} />
        </div>

        <div className="p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900">Brokers</h1>
              <div className="flex gap-2">
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
                  <Plus size={16} />
                  Add Broker
                </button>
              </div>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {summaryData.map((item, index) => (
                <div key={index} className={`p-4 rounded-lg border border-gray-200 ${item.bgColor}`}>
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{item.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{item.value}</p>
                    </div>
                    <div className="p-2 rounded-lg bg-white">
                      {item.icon}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search brokers..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="flex gap-4">
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="All">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                  </select>

                  <select
                    value={filterSpecialization}
                    onChange={(e) => setFilterSpecialization(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="All">All Specializations</option>
                    <option value="Residential">Residential</option>
                    <option value="Villas">Villas</option>
                    <option value="Commercial">Commercial</option>
                    <option value="Plots">Plots</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Brokers Grid */}
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                {currentBrokers.length === 0 ? (
                  <div className="col-span-full text-center py-12">
                    <Users size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No brokers found</h3>
                    <p className="text-gray-500">No brokers match your current search criteria.</p>
                  </div>
                ) : (
                  currentBrokers.map((broker) => (
                <div key={broker.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users size={24} className="text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{broker.full_name}</h3>
                        <p className="text-sm text-gray-500">{broker.experience_years || 0} years experience</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleViewBroker(broker.id)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="View Details"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => handleEditBroker(broker.id)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Edit Broker"
                      >
                        <Edit size={16} />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                        <MoreVertical size={16} />
                      </button>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1">
                        {renderStars(4.5)}
                        <span className="text-sm text-gray-600 ml-1">
                          4.5 (0 reviews)
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        broker.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {broker.status ? broker.status.charAt(0).toUpperCase() + broker.status.slice(1) : 'Unknown'}
                      </span>
                    </div>

                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Phone size={14} />
                      <span>{broker.contact_number}</span>
                    </div>

                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Mail size={14} />
                      <span>{broker.email}</span>
                    </div>

                    <div className="flex items-start space-x-2 text-sm text-gray-600">
                      <MapPin size={14} className="mt-0.5" />
                      <span className="line-clamp-2">{broker.office_address || 'No address provided'}</span>
                    </div>

                    <div className="pt-3 border-t border-gray-100">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">License Number</p>
                          <p className="font-semibold text-gray-900">{broker.license_number || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Specialization</p>
                          <p className="font-semibold text-gray-900">{broker.specialization || 'General'}</p>
                        </div>
                      </div>
                    </div>

                    {broker.specialization && (
                      <div className="flex flex-wrap gap-1">
                        <span className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">
                          {broker.specialization}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                  ))
                )}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>

                {Array.from({ length: totalPages }, (_, index) => (
                  <button
                    key={index + 1}
                    onClick={() => setCurrentPage(index + 1)}
                    className={`px-3 py-2 border rounded-lg ${
                      currentPage === index + 1
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {index + 1}
                  </button>
                ))}

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
